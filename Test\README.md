# ChatGPT API Improvements Test Suite

This test suite validates the improvements made to the ChatGPT API integration, specifically focusing on:

1. **Enhanced Prompt Engineering** - Better extraction of company names and document years
2. **Multi-Model Fallback System** - Cost-effective fallback from GPT-4o-mini → GPT-4.1-mini → GPT-4o
3. **Critical Field Validation** - Ensures mandatory fields are never empty or "Unknown"

## Files Overview

### Test Scripts

- **`test_chatgpt_improvements.py`** - Comprehensive test suite with multiple scenarios
- **`quick_test.py`** - Fast test for rapid iteration during development
- **`test_config.json`** - Configuration file with test scenarios and expected results

### Test Scenarios

The test suite includes various challenging scenarios:

1. **Standard Cases** - Well-formatted documents with clear information
2. **Minimal Information** - Documents with limited company/date information
3. **Hidden Data** - Company names buried in document sections
4. **Email Domain Extraction** - Company name only available in email addresses
5. **Edge Cases** - Documents without clear years (fallback testing)
6. **Out-of-Spec Parameters** - Technical documents with compliance issues
7. **Batch Processing** - Multiple documents from single email

## Prerequisites

1. **OpenAI API Key** - Set the `OPENAI_API_KEY` environment variable
2. **Python Dependencies** - Ensure all required packages are installed
3. **Core Modules** - The test scripts import from the `core` directory

## Running Tests

### Quick Test (Recommended for Development)

```bash
cd Test
python quick_test.py
```

This runs a single test case to quickly validate the basic functionality.

### Comprehensive Test Suite

```bash
cd Test
python test_chatgpt_improvements.py
```

This runs all test scenarios and provides detailed results.

### Setting Up Environment

```bash
# Set your OpenAI API key
export OPENAI_API_KEY="your-api-key-here"

# Or on Windows
set OPENAI_API_KEY=your-api-key-here
```

## Understanding Test Results

### Success Criteria

For each test, the following must be true for a **PASS**:

1. **Critical Fields Valid** - `company_name` and `document_year` must be extracted
2. **Company Match** - Extracted company name matches expected (case-insensitive, partial match allowed)
3. **Year Match** - Extracted year matches expected exactly
4. **Type Match** - Document type classification is reasonable

### Sample Output

```
TEST 1: Certificate of Analysis - Clear Data
============================================================
⏱️  Processing Time: 2.34s
📄 Document Type: Certificate of Analysis
🏢 Company Name: TechShop
📅 Document Year: 2023
✅ Critical Fields Valid: True

📊 VALIDATION:
   Company Match: ✅ (Expected: TechShop)
   Year Match: ✅ (Expected: 2023)
   Type Match: ✅ (Expected: certificate of analysis)
   Overall Success: ✅
```

### Fallback Mechanism Testing

The tests will show which model was used:

```
INFO:core.interpreter.chatgpt_api:Attempting analysis with gpt-4o-mini (attempt 1/3)
INFO:core.interpreter.chatgpt_api:✅ Successful extraction with gpt-4o-mini - all critical fields present
```

If fallback occurs:

```
INFO:core.interpreter.chatgpt_api:⚠️ gpt-4o-mini missing critical fields: ['company_name']
INFO:core.interpreter.chatgpt_api:Enhanced prompt for retry - focusing on missing fields: ['company_name']
INFO:core.interpreter.chatgpt_api:Attempting analysis with gpt-4.1-mini (attempt 2/3)
INFO:core.interpreter.chatgpt_api:✅ Successful extraction with gpt-4.1-mini - all critical fields present
```

## Customizing Tests

### Adding New Test Scenarios

Edit `test_config.json` to add new test cases:

```json
{
  "name": "Your Test Name",
  "description": "Description of what this tests",
  "mail_body": "Email content...",
  "pdf_text": "Document content...",
  "expected": {
    "company_name": "Expected Company",
    "document_year": 2024,
    "doc_type": "expected type"
  }
}
```

### Modifying Test Settings

Adjust test behavior in `test_config.json`:

```json
"test_settings": {
  "timeout_seconds": 120,
  "use_batch_manager": false,
  "language": "English",
  "validate_critical_fields": true,
  "show_detailed_output": true
}
```

## Troubleshooting

### Common Issues

1. **API Key Not Set**
   ```
   ❌ ERROR: OPENAI_API_KEY environment variable not set!
   ```
   Solution: Set your OpenAI API key as an environment variable.

2. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'core'
   ```
   Solution: Run tests from the `Test` directory, not the root directory.

3. **Rate Limiting**
   ```
   Rate limited (429). Waiting 60s before retry
   ```
   Solution: The system will automatically handle rate limits. Wait for completion.

4. **All Models Fail**
   ```
   ❌ All models failed to extract critical fields
   ```
   Solution: Check if the test scenario has sufficient information for extraction.

### Performance Expectations

- **Quick Test**: ~2-5 seconds per test
- **Comprehensive Suite**: ~2-5 minutes total (depending on API response times)
- **Fallback Scenarios**: May take longer as multiple models are tried

## Interpreting Results

### Success Rate Guidelines

- **90-100%**: Excellent - Ready for production
- **80-89%**: Good - Minor prompt adjustments may be needed
- **70-79%**: Acceptable - Review failed cases and improve prompts
- **<70%**: Needs work - Significant prompt engineering required

### Next Steps After Testing

1. **All Tests Pass**: Deploy to production and monitor real-world performance
2. **Some Tests Fail**: Review failed scenarios and adjust prompts accordingly
3. **Many Tests Fail**: Consider revising the prompt engineering approach

## Cost Considerations

The fallback system is designed to be cost-effective:

1. **GPT-4o-mini**: Cheapest, tried first (~$0.15/1M tokens)
2. **GPT-4.1-mini**: Mid-tier fallback (~$0.30/1M tokens)  
3. **GPT-4o**: Most expensive, last resort (~$5.00/1M tokens)

Most documents should be successfully processed by the cheaper models, with expensive models only used when necessary.
