#!/usr/bin/env python3
"""
Test script specifically for CV/Personal document handling.
"""

import sys
import os
import time

# Add the parent directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.interpreter.chatgpt_api import (
    analyze_mail_and_pdf,
    _validate_critical_fields
)

def test_cv_case():
    """Test CV handling to ensure it doesn't extract random company names."""
    
    print("🧪 Testing CV/Personal Document Handling")
    print("="*50)
    
    mail_body = """
Dear Hiring Manager,

Please find attached my CV for the Software Engineer position.
I am available for interview at your convenience.

Best regards,
<PERSON>
Phone: +46-70-123-4567
Email: <EMAIL>
    """
    
    pdf_text = """
CURRICULUM VITAE

John Smith
Software Engineer
Phone: +46-70-123-4567
Email: <EMAIL>

EXPERIENCE:
2020-2024: Senior Developer at TechCorp AB
2018-2020: Junior Developer at StartupXYZ

EDUCATION:
2018: Master's in Computer Science, KTH Royal Institute

SKILLS:
Python, JavaScript, React, Node.js
    """
    
    print("📧 Email Body:")
    print(mail_body.strip())
    print("\n📄 PDF Content:")
    print(pdf_text.strip())
    print("\n" + "="*50)
    
    start_time = time.time()
    
    try:
        result = analyze_mail_and_pdf(
            mail_body=mail_body,
            pdf_text=pdf_text,
            language="English",
            use_batch_manager=False
        )
        
        processing_time = time.time() - start_time
        
        # Validate results
        is_valid, missing_fields = _validate_critical_fields(result)
        
        print(f"⏱️  Processing Time: {processing_time:.2f}s")
        print(f"✅ Critical Fields Valid: {is_valid}")
        
        if not is_valid:
            print(f"❌ Missing Fields: {missing_fields}")
        
        # Show extracted data
        extracted = result.get("extracted_fields", {})
        company_name = extracted.get("company_name", "N/A")
        document_year = extracted.get("document_year", "N/A")
        doc_type = result.get("doc_type", "N/A")
        
        print(f"\n📊 EXTRACTED DATA:")
        print(f"   🏢 Company: {company_name}")
        print(f"   📅 Year: {document_year}")
        print(f"   📄 Type: {doc_type}")
        
        # Show summary
        summary = result.get("summary", "")
        if summary:
            print(f"\n📝 SUMMARY:")
            print(f"   {summary}")
        
        # Validation
        is_personal = "personal" in company_name.lower() or company_name.lower() in ["john smith", "john", "smith"]
        is_not_techcorp = "techcorp" not in company_name.lower()
        is_cv = "cv" in doc_type.lower() or "curriculum" in doc_type.lower()
        
        print(f"\n🎯 VALIDATION:")
        print(f"   Personal Document Recognition: {'✅' if is_personal else '❌'}")
        print(f"   Avoided TechCorp Extraction: {'✅' if is_not_techcorp else '❌'}")
        print(f"   CV Type Recognition: {'✅' if is_cv else '❌'}")
        
        success = is_valid and (is_personal or is_not_techcorp) and is_cv
        print(f"   Overall: {'✅ SUCCESS' if success else '❌ NEEDS WORK'}")
        
        if success:
            print("\n🎉 CV test passed! Personal document handling is working correctly.")
        else:
            print("\n⚠️  CV test revealed issues. The system may be extracting company names from personal documents.")
            
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")

if __name__ == "__main__":
    # Check API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ ERROR: OPENAI_API_KEY environment variable not set!")
        sys.exit(1)
    
    test_cv_case()
