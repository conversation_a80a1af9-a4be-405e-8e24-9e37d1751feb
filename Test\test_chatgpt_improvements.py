#!/usr/bin/env python3
"""
Comprehensive test suite for ChatGPT API improvements.

This script tests:
1. Improved prompt engineering for better company name and document year extraction
2. Multi-model fallback mechanism (GPT-4o-mini → GPT-4.1-mini → GPT-4o)
3. Edge cases and failure scenarios
4. Validation of critical field extraction

Run this script to validate the improvements before deploying to production.
"""

import sys
import os
import json
import time
from typing import Dict, Any, List, Tuple
from datetime import datetime

# Add the parent directory to the path so we can import core modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.interpreter.chatgpt_api import (
    analyze_mail_and_pdf, 
    analyze_mail_and_batch_pdfs,
    _validate_critical_fields,
    FALLBACK_MODELS
)

# Test data with various scenarios
TEST_SCENARIOS = [
    {
        "name": "Certificate of Analysis - Clear Data",
        "mail_body": """
Dear Quality Team,

Please find attached the Certificate of Analysis for batch 6098403.
This material was manufactured on 2023-11-08 by TechShop AB.

Contact: <PERSON> (<EMAIL>, +46-8-123-4567)
Urgent review required by Friday.

Best regards,
Quality Department
        """,
        "pdf_text": """
CERTIFICATE OF ANALYSIS
TechShop AB
Batch Number: 6098403
Manufacturing Date: 2023-11-08
Product: High-Grade Steel
Test Date: 2023-11-10

RESULTS:
Carbon Content: 0.45% (Spec: 0.40-0.50%) ✓
Tensile Strength: 580 MPa (Spec: 550-600 MPa) ✓
Hardness: 45 HRC (Spec: 40-50 HRC) ✓

All parameters within specification.
        """,
        "expected_company": "TechShop",
        "expected_year": 2023,
        "expected_doc_type": "certificate of analysis"
    },
    {
        "name": "Invoice - Minimal Company Info",
        "mail_body": """
Invoice attached.
Contact <EMAIL> for questions.
        """,
        "pdf_text": """
INVOICE #INV-2024-001
Date: 2024-01-15
From: ACME Corporation LLC
To: Customer XYZ

Amount: $1,500.00
Due Date: 2024-02-15
        """,
        "expected_company": "ACME Corporation",
        "expected_year": 2024,
        "expected_doc_type": "invoice"
    },
    {
        "name": "Safety Data Sheet - Hidden Company",
        "mail_body": """
SDS for chemical product attached.
Emergency contact: ******-CHEM-911
        """,
        "pdf_text": """
SAFETY DATA SHEET
Product: Industrial Solvent X
Revision Date: 2022-06-15

Section 1: Identification
Manufacturer: ChemCorp Industries Inc.
Address: 123 Chemical Lane
Emergency Phone: ******-CHEM-911

Section 2: Hazard Identification
Flammable liquid and vapor
        """,
        "expected_company": "ChemCorp Industries",
        "expected_year": 2022,
        "expected_doc_type": "safety data sheet"
    },
    {
        "name": "Difficult Case - Company in Email Domain Only",
        "mail_body": """
Document attached as requested.
Please review and confirm receipt.

From: <EMAIL>
        """,
        "pdf_text": """
TEST REPORT
Report Number: TR-2023-456
Test Date: 2023-09-20

Sample Description: Metal Component
Test Method: ASTM D123

Results:
Parameter A: 15.2 (Acceptable)
Parameter B: 8.7 (Acceptable)
        """,
        "expected_company": "hiddentech",  # Should extract from email domain
        "expected_year": 2023,
        "expected_doc_type": "test report"
    },
    {
        "name": "Edge Case - No Clear Year",
        "mail_body": """
Please review the attached document.
Contact: <EMAIL>
        """,
        "pdf_text": """
QUALITY CERTIFICATE
NewCompany Organization
Certificate Number: QC-ABC-123

Product: Widget Type A
Quality Grade: Premium

All tests passed according to internal standards.
        """,
        "expected_company": "NewCompany",
        "expected_year": 2024,  # Should fallback to current year
        "expected_doc_type": "quality certificate"
    }
]

# Batch test scenario
BATCH_TEST_SCENARIO = {
    "name": "Batch Processing - Multiple CoA Documents",
    "mail_body": """
Dear Team,

Please find attached 3 Certificates of Analysis for recent batches.
All from TechShop AB, manufactured in November 2023.

Contact: <EMAIL>
Review deadline: End of week.

Best regards,
Quality Control
    """,
    "documents": [
        {
            "filename": "CoA_Batch_6098401.pdf",
            "text": """
CERTIFICATE OF ANALYSIS - TechShop AB
Batch: 6098401, Mfg Date: 2023-11-05
Carbon: 0.42% ✓, Tensile: 575 MPa ✓
All parameters within spec.
            """
        },
        {
            "filename": "CoA_Batch_6098402.pdf", 
            "text": """
CERTIFICATE OF ANALYSIS - TechShop AB
Batch: 6098402, Mfg Date: 2023-11-06
Carbon: 0.51% ❌ (Spec: 0.40-0.50%)
Tensile: 590 MPa ✓
ATTENTION REQUIRED: Carbon content out of specification.
            """
        },
        {
            "filename": "CoA_Batch_6098403.pdf",
            "text": """
CERTIFICATE OF ANALYSIS - TechShop AB
Batch: 6098403, Mfg Date: 2023-11-07
Carbon: 0.48% ✓, Tensile: 585 MPa ✓
All parameters within spec.
            """
        }
    ],
    "expected_company": "TechShop",
    "expected_year": 2023
}


def run_single_test(scenario: Dict[str, Any], test_num: int) -> Tuple[bool, Dict[str, Any]]:
    """Run a single test scenario and validate results."""
    print(f"\n{'='*60}")
    print(f"TEST {test_num}: {scenario['name']}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # Run the analysis
        result = analyze_mail_and_pdf(
            mail_body=scenario["mail_body"],
            pdf_text=scenario["pdf_text"],
            language="English",
            use_batch_manager=False  # Direct testing
        )
        
        processing_time = time.time() - start_time
        
        # Validate critical fields
        is_valid, missing_fields = _validate_critical_fields(result)
        
        # Extract results for comparison
        extracted_fields = result.get("extracted_fields", {})
        company_name = extracted_fields.get("company_name", "").lower()
        document_year = extracted_fields.get("document_year")
        doc_type = result.get("doc_type", "").lower()
        
        # Check expectations
        expected_company = scenario["expected_company"].lower()
        expected_year = scenario["expected_year"]
        expected_doc_type = scenario["expected_doc_type"].lower()
        
        company_match = expected_company in company_name or company_name in expected_company
        # Handle both string and integer year formats
        try:
            year_match = int(document_year) == expected_year
        except (ValueError, TypeError):
            year_match = False
        type_match = any(word in doc_type for word in expected_doc_type.split())
        
        success = is_valid and company_match and year_match and type_match
        
        # Print results
        print(f"⏱️  Processing Time: {processing_time:.2f}s")
        print(f"📄 Document Type: {result.get('doc_type', 'N/A')}")
        print(f"🏢 Company Name: {extracted_fields.get('company_name', 'N/A')}")
        print(f"📅 Document Year: {extracted_fields.get('document_year', 'N/A')}")
        print(f"✅ Critical Fields Valid: {is_valid}")
        
        if not is_valid:
            print(f"❌ Missing Fields: {missing_fields}")
        
        print(f"\n📊 VALIDATION:")
        print(f"   Company Match: {'✅' if company_match else '❌'} (Expected: {scenario['expected_company']})")
        print(f"   Year Match: {'✅' if year_match else '❌'} (Expected: {expected_year})")
        print(f"   Type Match: {'✅' if type_match else '❌'} (Expected: {expected_doc_type})")
        print(f"   Overall Success: {'✅' if success else '❌'}")
        
        # Show summary (first 200 chars)
        summary = result.get("summary", "")
        if summary:
            print(f"\n📝 Summary Preview:")
            print(f"   {summary[:200]}{'...' if len(summary) > 200 else ''}")
        
        return success, result
        
    except Exception as e:
        print(f"❌ TEST FAILED WITH EXCEPTION: {e}")
        return False, {"error": str(e)}


def run_batch_test() -> Tuple[bool, Dict[str, Any]]:
    """Run the batch processing test."""
    print(f"\n{'='*60}")
    print(f"BATCH TEST: {BATCH_TEST_SCENARIO['name']}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # Run batch analysis
        result = analyze_mail_and_batch_pdfs(
            mail_body=BATCH_TEST_SCENARIO["mail_body"],
            document_texts=BATCH_TEST_SCENARIO["documents"],
            language="English",
            use_batch_manager=False  # Direct testing
        )
        
        processing_time = time.time() - start_time
        
        # Validate critical fields
        is_valid, missing_fields = _validate_critical_fields(result)
        
        # Extract results
        extracted_fields = result.get("extracted_fields", {})
        company_name = extracted_fields.get("company_name", "").lower()
        document_year = extracted_fields.get("document_year")
        
        # Check expectations
        expected_company = BATCH_TEST_SCENARIO["expected_company"].lower()
        expected_year = BATCH_TEST_SCENARIO["expected_year"]
        
        company_match = expected_company in company_name
        # Handle both string and integer year formats
        try:
            year_match = int(document_year) == expected_year
        except (ValueError, TypeError):
            year_match = False
        
        success = is_valid and company_match and year_match
        
        # Print results
        print(f"⏱️  Processing Time: {processing_time:.2f}s")
        print(f"📄 Document Type: {result.get('doc_type', 'N/A')}")
        print(f"🏢 Company Name: {extracted_fields.get('company_name', 'N/A')}")
        print(f"📅 Document Year: {extracted_fields.get('document_year', 'N/A')}")
        print(f"✅ Critical Fields Valid: {is_valid}")
        
        if not is_valid:
            print(f"❌ Missing Fields: {missing_fields}")
        
        print(f"\n📊 VALIDATION:")
        print(f"   Company Match: {'✅' if company_match else '❌'} (Expected: {expected_company})")
        print(f"   Year Match: {'✅' if year_match else '❌'} (Expected: {expected_year})")
        print(f"   Overall Success: {'✅' if success else '❌'}")
        
        # Show summary
        summary = result.get("summary", "")
        if summary:
            print(f"\n📝 Batch Summary:")
            print(f"   {summary}")
        
        return success, result
        
    except Exception as e:
        print(f"❌ BATCH TEST FAILED WITH EXCEPTION: {e}")
        return False, {"error": str(e)}


def main():
    """Run all tests and provide summary."""
    print("🚀 Starting ChatGPT API Improvements Test Suite")
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🤖 Available Models: {', '.join(FALLBACK_MODELS)}")
    
    # Check if API key is available
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ ERROR: OPENAI_API_KEY environment variable not set!")
        print("Please set your OpenAI API key before running tests.")
        return
    
    results = []
    total_tests = len(TEST_SCENARIOS) + 1  # +1 for batch test
    
    # Run individual tests
    for i, scenario in enumerate(TEST_SCENARIOS, 1):
        success, result = run_single_test(scenario, i)
        results.append(("Single", scenario["name"], success, result))
    
    # Run batch test
    batch_success, batch_result = run_batch_test()
    results.append(("Batch", BATCH_TEST_SCENARIO["name"], batch_success, batch_result))
    
    # Print summary
    print(f"\n{'='*80}")
    print("📊 TEST SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(1 for _, _, success, _ in results if success)
    failed = total_tests - passed
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/total_tests)*100:.1f}%")
    
    print(f"\n📋 DETAILED RESULTS:")
    for test_type, name, success, result in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} [{test_type}] {name}")
        if not success and "error" in result:
            print(f"      Error: {result['error']}")
    
    if failed > 0:
        print(f"\n⚠️  {failed} test(s) failed. Please review the results above.")
        print("Consider adjusting prompts or fallback logic if critical fields are missing.")
    else:
        print(f"\n🎉 All tests passed! The improvements are working correctly.")
    
    print(f"\n💡 Next Steps:")
    print("   1. Review any failed tests and adjust prompts if needed")
    print("   2. Test with real documents in your environment")
    print("   3. Monitor performance and extraction accuracy in production")


if __name__ == "__main__":
    main()
