#!/usr/bin/env python3
"""
Quick test script for ChatGPT API improvements.

This is a simplified test that focuses on the most critical functionality:
- Company name extraction
- Document year extraction  
- Model fallback mechanism

Use this for rapid iteration and testing during development.
"""

import sys
import os
import json
import time

# Add the parent directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.interpreter.chatgpt_api import (
    analyze_mail_and_pdf,
    _validate_critical_fields,
    FALLBACK_MODELS
)

def quick_test():
    """Run a quick test with a simple document."""
    
    print("🔬 Quick Test - ChatGPT API Improvements")
    print(f"🤖 Models: {' → '.join(FALLBACK_MODELS)}")
    print("="*50)
    
    # Simple test case
    mail_body = """
Dear Team,

Please review the attached Certificate of Analysis for batch ABC123.
This was manufactured by TechCorp AB on 2023-10-15.

Contact: <EMAIL>
Phone: +46-8-555-0123

Best regards,
Quality Department
    """
    
    pdf_text = """
CERTIFICATE OF ANALYSIS
TechCorp AB
Manufacturing Date: 2023-10-15
Batch Number: ABC123
Product: Steel Grade A

Test Results:
- Carbon Content: 0.45% (Spec: 0.40-0.50%) ✓
- Tensile Strength: 580 MPa (Spec: 550-600 MPa) ✓

All parameters within specification.
Approved by: John Smith, Quality Manager
    """
    
    print("📧 Email Body:")
    print(mail_body.strip())
    print("\n📄 PDF Content:")
    print(pdf_text.strip())
    print("\n" + "="*50)
    
    # Run the test
    start_time = time.time()
    
    try:
        result = analyze_mail_and_pdf(
            mail_body=mail_body,
            pdf_text=pdf_text,
            language="English",
            use_batch_manager=False
        )
        
        processing_time = time.time() - start_time
        
        # Validate results
        is_valid, missing_fields = _validate_critical_fields(result)
        
        print(f"⏱️  Processing Time: {processing_time:.2f}s")
        print(f"✅ Critical Fields Valid: {is_valid}")
        
        if not is_valid:
            print(f"❌ Missing Fields: {missing_fields}")
        
        # Show extracted data
        extracted = result.get("extracted_fields", {})
        print(f"\n📊 EXTRACTED DATA:")
        print(f"   🏢 Company: {extracted.get('company_name', 'N/A')}")
        print(f"   📅 Year: {extracted.get('document_year', 'N/A')}")
        print(f"   📄 Type: {result.get('doc_type', 'N/A')}")
        
        # Show other fields
        other_fields = {k: v for k, v in extracted.items() 
                       if k not in ['company_name', 'document_year']}
        if other_fields:
            print(f"   📋 Other Fields: {list(other_fields.keys())}")
        
        # Show summary
        summary = result.get("summary", "")
        if summary:
            print(f"\n📝 SUMMARY:")
            print(f"   {summary}")
        
        # Final assessment
        expected_company = "techcorp"
        expected_year = 2023
        
        company_name = extracted.get("company_name", "").lower()
        document_year = extracted.get("document_year")

        company_ok = expected_company in company_name
        # Handle both string and integer year formats
        try:
            year_ok = int(document_year) == expected_year
        except (ValueError, TypeError):
            year_ok = False
        
        print(f"\n🎯 VALIDATION:")
        print(f"   Company Extraction: {'✅' if company_ok else '❌'} (Got: '{company_name}', Expected: '{expected_company}')")
        print(f"   Year Extraction: {'✅' if year_ok else '❌'} (Got: {document_year} ({type(document_year).__name__}), Expected: {expected_year})")
        print(f"   Overall: {'✅ SUCCESS' if (is_valid and company_ok and year_ok) else '❌ NEEDS WORK'}")
        
        if is_valid and company_ok and year_ok:
            print("\n🎉 Quick test passed! The improvements are working.")
        else:
            print("\n⚠️  Quick test revealed issues. Check the results above.")
            
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        print("Check your OpenAI API key and network connection.")

if __name__ == "__main__":
    # Check API key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ ERROR: OPENAI_API_KEY environment variable not set!")
        print("Please set your OpenAI API key before running the test.")
        sys.exit(1)
    
    quick_test()
