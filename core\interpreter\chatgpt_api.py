"""OpenAI ChatGPT-4o integration helpers with advanced rate limiting and batching.

This module provides intelligent document processing with OpenAI's API, featuring:
- Request queue system with rate limiting
- Intelligent batching for multiple documents
- Enhanced retry logic with exponential backoff
- Proper handling of 429 (Too Many Requests) errors

Environment variables
---------------------
OPENAI_API_KEY
    The secret API key used for authenticating with the OpenAI service.

Any networking or JSON related error is retried with exponential back-off up to
``MAX_RETRIES`` times. If all attempts fail the function returns a minimal
fallback dict so that the caller can continue processing without crashing.
"""

from __future__ import annotations

from dotenv import load_dotenv
load_dotenv()

from typing import Dict, Any, List, Optional, Tuple
import os
import json
import time
import logging
import requests
import threading
from queue import Queue, Empty
from dataclasses import dataclass
from datetime import datetime, timedelta

__all__ = ["analyze_mail_and_pdf", "analyze_mail_and_batch_pdfs", "BatchRequestManager"]

log = logging.getLogger(__name__)

OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

# Multi-model fallback configuration (cost-effective: cheapest to most expensive)
FALLBACK_MODELS = [
    #"gpt-4o-mini",      # Cheapest, fastest - try first
    "gpt-4.1-mini",     # Mid-tier option
    "gpt-4o"            # Most expensive, most capable - last resort
]

# Default model (first in fallback chain)
MODEL = FALLBACK_MODELS[0]
TIMEOUT = 60  # Increased timeout for batch requests
MAX_RETRIES = 5  # Increased retries for rate limiting
BACKOFF_SECS = 2

# Rate limiting configuration
REQUESTS_PER_MINUTE = 50  # Conservative limit for GPT-4o
MIN_REQUEST_INTERVAL = 60.0 / REQUESTS_PER_MINUTE  # Minimum seconds between requests
RATE_LIMIT_BACKOFF = 60  # Seconds to wait on 429 error


@dataclass
class BatchRequest:
    """Represents a single request in the batch processing queue."""
    request_id: str
    mail_body: str
    document_texts: List[Dict[str, str]]  # List of {filename, text} dicts
    language: str
    is_batch: bool
    timestamp: datetime
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    completed: bool = False


class BatchRequestManager:
    """Manages OpenAI API requests with intelligent batching and rate limiting."""

    def __init__(self):
        self._request_queue = Queue()
        self._results = {}
        self._last_request_time = 0
        self._lock = threading.Lock()
        self._processing = False

    def add_request(self, request_id: str, mail_body: str, document_texts: List[Dict[str, str]],
                   language: str = "English", is_batch: bool = False) -> str:
        """Add a request to the processing queue."""
        request = BatchRequest(
            request_id=request_id,
            mail_body=mail_body,
            document_texts=document_texts,
            language=language,
            is_batch=is_batch,
            timestamp=datetime.now()
        )
        self._request_queue.put(request)
        return request_id

    def get_result(self, request_id: str, timeout: float = 300) -> Optional[Dict[str, Any]]:
        """Get the result for a specific request, waiting up to timeout seconds."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            with self._lock:
                if request_id in self._results:
                    result = self._results.pop(request_id)
                    return result
            time.sleep(0.1)
        return None

    def process_queue(self) -> None:
        """Process all requests in the queue with proper rate limiting."""
        if self._processing:
            return

        self._processing = True
        try:
            while not self._request_queue.empty():
                try:
                    request = self._request_queue.get_nowait()
                    self._process_single_request(request)
                except Empty:
                    break
        finally:
            self._processing = False

    def _process_single_request(self, request: BatchRequest) -> None:
        """Process a single request with rate limiting."""
        # Ensure minimum interval between requests
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        if time_since_last < MIN_REQUEST_INTERVAL:
            sleep_time = MIN_REQUEST_INTERVAL - time_since_last
            log.info(f"Rate limiting: sleeping {sleep_time:.2f}s before next request")
            time.sleep(sleep_time)

        try:
            if request.is_batch and len(request.document_texts) > 1:
                result = self._process_batch_request(request)
            else:
                # Single document processing
                doc_text = request.document_texts[0]['text'] if request.document_texts else ""
                result = self._process_single_document(request.mail_body, doc_text, request.language)

            with self._lock:
                self._results[request.request_id] = result

        except Exception as e:
            log.error(f"Failed to process request {request.request_id}: {e}")
            with self._lock:
                self._results[request.request_id] = {
                    "doc_type": "Unknown",
                    "summary": f"Processing failed: {str(e)}",
                    "extracted_fields": {},
                }
        finally:
            self._last_request_time = time.time()

    def _process_batch_request(self, request: BatchRequest) -> Dict[str, Any]:
        """Process a batch request with multiple documents using intelligent model fallback."""
        # Combine all document texts into a single prompt
        combined_text = ""
        for i, doc in enumerate(request.document_texts, 1):
            filename = doc.get('filename', f'Document {i}')
            text = doc.get('text', '')
            combined_text += f"\n\n=== DOCUMENT {i}: {filename} ===\n{text}"

        messages = _build_messages(request.mail_body, combined_text, request.language, is_batch=True)

        try:
            # Use the new fallback mechanism
            result = _post_chat_with_fallback(messages, is_batch=True)
            return result
        except Exception as exc:
            log.error("ChatGPT batch analysis failed with all models – falling back to defaults: %s", exc)
            return {
                "doc_type": "Unknown",
                "summary": "ChatGPT batch analysis failed with all models.",
                "extracted_fields": {
                    "company_name": "Unknown",
                    "document_year": 2024,
                    "_fallback_used": True
                },
            }

    def _process_single_document(self, mail_body: str, pdf_text: str, language: str) -> Dict[str, Any]:
        """Process a single document with intelligent model fallback."""
        messages = _build_messages(mail_body, pdf_text, language, is_batch=False)

        try:
            # Use the new fallback mechanism
            result = _post_chat_with_fallback(messages, is_batch=False)
            return result
        except Exception as exc:
            log.error("ChatGPT analysis failed with all models – falling back to defaults: %s", exc)
            return {
                "doc_type": "Unknown",
                "summary": "ChatGPT analysis failed with all models.",
                "extracted_fields": {
                    "company_name": "Unknown",
                    "document_year": 2024,
                    "_fallback_used": True
                },
            }


# Global batch manager instance
_batch_manager = BatchRequestManager()


def _validate_critical_fields(result: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate that critical fields are properly extracted.

    Returns:
        Tuple of (is_valid, missing_fields)
    """
    missing_fields = []
    extracted_fields = result.get("extracted_fields", {})

    # Check company_name
    company_name = extracted_fields.get("company_name", "").strip()
    if not company_name or company_name.lower() in ["unknown", "null", "none", "", "n/a"]:
        missing_fields.append("company_name")

    # Check document_year
    document_year = extracted_fields.get("document_year")
    if not document_year:
        missing_fields.append("document_year")
    else:
        # Validate year format (should be 4-digit number)
        try:
            year_int = int(str(document_year))
            if year_int < 1900 or year_int > 2030:  # Reasonable year range
                missing_fields.append("document_year")
        except (ValueError, TypeError):
            missing_fields.append("document_year")

    return len(missing_fields) == 0, missing_fields


def _enhance_prompt_for_retry(messages: List[Dict[str, str]], missing_fields: List[str], attempt: int) -> List[Dict[str, str]]:
    """
    Enhance the system prompt for retry attempts with specific focus on missing fields.
    """
    enhanced_messages = messages.copy()

    # Add specific instructions for missing fields
    additional_instructions = "\n\n=== RETRY FOCUS ===\n"
    additional_instructions += f"ATTEMPT {attempt + 1}: The previous attempt failed to extract: {', '.join(missing_fields)}\n"
    additional_instructions += "CRITICAL: Pay extra attention to these specific fields:\n"

    if "company_name" in missing_fields:
        additional_instructions += (
            "• COMPANY_NAME: IMPORTANT - Only extract if document is from a BUSINESS/ORGANIZATION. "
            "For personal documents (CV, resume, personal letters), use 'Personal Document' or sender's name. "
            "For business documents, search headers, footers, logos, email signatures, contact info. "
            "Look for manufacturer, supplier, vendor, or business entity names. "
            "Do NOT force random company names for personal documents.\n"
        )

    if "document_year" in missing_fields:
        additional_instructions += (
            "• DOCUMENT_YEAR: Search for ANY 4-digit year in the document. "
            "Check manufacturing dates, test dates, issue dates, creation dates, "
            "expiry dates, or any timestamp. If absolutely no year is found, "
            "use the current year (2024) as fallback.\n"
        )

    additional_instructions += "\nThese fields are MANDATORY and must be extracted successfully."

    # Enhance the system prompt
    enhanced_messages[0]["content"] += additional_instructions

    return enhanced_messages


_SYSTEM_PROMPT = (
    "You are an expert document processing assistant for email automation. Your primary task is to analyze email + PDF content and extract critical information with 100% accuracy.\n\n"

    "=== CRITICAL EXTRACTION REQUIREMENTS ===\n"
    "These fields are MANDATORY and must NEVER be empty, null, or 'Unknown':\n\n"

    "1. COMPANY_NAME (HIGHEST PRIORITY):\n"
    "   • IMPORTANT: Only extract if document is from/about a BUSINESS/ORGANIZATION\n"
    "   • For PERSONAL documents (CV, resume, personal letters): Use 'Personal Document' or sender's name\n"
    "   • For BUSINESS documents: Extract manufacturer, supplier, vendor, or organization name\n"
    "   • Search order: document headers → letterheads → email signatures → contact sections\n"
    "   • Clean format: Remove legal suffixes (AB, Inc, Ltd, LLC, GmbH, etc.)\n"
    "   • Examples: 'TechShop AB' → 'TechShop', 'Acme Corp LLC' → 'Acme Corp'\n"
    "   • If multiple companies present, choose the PRIMARY one (manufacturer over distributor)\n"
    "   • AVOID: Random company names, email domains for personal docs, generic terms\n\n"

    "2. DOCUMENT_YEAR (HIGHEST PRIORITY):\n"
    "   • Extract 4-digit year most relevant to the document's purpose\n"
    "   • Priority order: manufacturing_date → test_date → issue_date → creation_date → current_year\n"
    "   • For CoA/Certificates: Use manufacturing or test year\n"
    "   • For Invoices: Use invoice date year\n"
    "   • For Safety Sheets: Use revision or issue year\n"
    "   • FALLBACK: If no dates found, use current year (2024)\n\n"

    "=== EMAIL ANALYSIS ===\n"
    "• Extract ALL instructions, requests, and specific guidance from email body\n"
    "• Capture complete contact information: phone numbers, extensions, emails, emergency contacts\n"
    "• Identify deadlines, urgency indicators, special handling requirements\n"
    "• Include actual URLs/links mentioned (never use generic 'provided link' text)\n\n"

    "=== DETAILED EXTRACTION RULES ===\n"
    "• Batch/Lot/Serial Numbers: Extract ONLY alphanumeric identifiers (e.g., '6098403', 'ABC123'), NOT dates\n"
    "• Dates: Extract as separate specific fields (manufacturing_date, expiry_date, test_date, invoice_date)\n"
    "• Example: 'Batch: 2018-11-08' → batch_number: '6098403' (find actual ID), manufacturing_date: '2018-11-08'\n"
    "• Use descriptive field names: batch_number, invoice_number, po_number, certificate_number, etc.\n\n"

    "=== INTELLIGENT ANALYSIS ===\n"
    "• For technical documents (CoA, test reports, safety sheets): Compare results against specifications\n"
    "• Report ONLY concerning parameters (out-of-spec, close to limits, safety-critical)\n"
    "• Summarize compliant parameters: 'All other parameters within specification'\n"
    "• Provide clear actionable guidance:\n"
    "  - All compliant: 'Optional review - all parameters within specification'\n"
    "  - Minor issues: 'Attention recommended for [specific areas]'\n"
    "  - Major issues: 'Attention required for [specific parameters]'\n"
    "  - Critical: 'URGENT review - critical safety/quality issues identified'\n\n"

    "=== COMPACT SUMMARY FORMAT (EXACT STRUCTURE REQUIRED) ===\n\n"
    "--- DOCUMENT INFO ---\n"
    "• Document Type: [specific type]\n"
    "• Company: [extracted company name OR 'Personal Document' for CVs/personal docs]\n"
    "• Key Identifiers: [most important ID only]\n"
    "• Relevant Dates: [most important date only]\n\n"

    "--- KEY FINDINGS ---\n"
    "• [ONLY critical issues, out-of-spec values, or safety concerns - max 2 lines]\n"
    "• [For compliant docs: 'No issues identified' or 'All parameters within spec']\n\n"

    "--- EMAIL INSTRUCTIONS ---\n"
    "• Contact: [primary contact only]\n"
    "• Deadline: [if any]\n"
    "• Instructions: [key action requested - be brief]\n"
    "• Links: [actual URLs only if mentioned]\n\n"

    "--- ACTION REQUIRED ---\n"
    "• [Primary action needed - ONE line maximum]\n"
    "• Priority: [High/Normal/Low]\n\n"

    "Keep ALL sections COMPACT. Maximum 8-10 lines total for entire summary.\n"
    "Use bullet points (•) and proper spacing between sections.\n\n"

    "Return ONLY valid JSON: {\"doc_type\": \"\", \"summary\": \"\", \"extracted_fields\": {}}"
)

_BATCH_SYSTEM_PROMPT = (
    "You are an expert document processing assistant. Analyze email + MULTIPLE PDFs to create ONE COMPACT summary for the entire batch.\n\n"

    "=== CRITICAL EXTRACTION REQUIREMENTS ===\n"
    "1. COMPANY_NAME: Extract from documents OR use 'Personal Document' for CVs/personal docs\n"
    "2. DOCUMENT_YEAR: Extract most relevant year OR use 2024 as fallback\n\n"

    "=== BATCH PROCESSING RULES ===\n"
    "• Create ONE consolidated summary for ALL documents\n"
    "• List documents briefly (filename: short description)\n"
    "• Highlight ONLY critical issues or concerns\n"
    "• Summarize compliant documents in ONE line\n"
    "• Keep total summary under 10 lines\n\n"

    "=== ULTRA-COMPACT BATCH FORMAT ===\n\n"
    "--- DOCUMENT INFO ---\n"
    "• Document Type: [type] ([count] documents)\n"
    "• Company: [name OR 'Personal Document']\n"
    "• Documents: [filename1: brief], [filename2: brief], [etc.]\n\n"

    "--- KEY FINDINGS ---\n"
    "• [ONLY list critical issues - max 2 lines]\n"
    "• [Compliant docs: 'X documents within spec' or 'No issues identified']\n\n"

    "--- EMAIL INSTRUCTIONS ---\n"
    "• Contact: [primary contact]\n"
    "• Deadline: [if any]\n"
    "• Instructions: [key action - brief]\n\n"

    "CRITICAL: Create ONE summary for the entire batch, NOT individual summaries per document.\n"
    "Maximum 8-10 lines total. Use bullet points (•) and proper spacing.\n\n"

    "Return ONLY valid JSON: {\"doc_type\": \"\", \"summary\": \"\", \"extracted_fields\": {}}"
)


def _build_messages(mail_body: str, pdf_text: str, language: str = "English", is_batch: bool = False) -> List[Dict[str, str]]:
    """Compose the messages list for the Chat Completions endpoint."""

    user_prompt = (
        "EMAIL BODY:\n" + mail_body.strip() + "\n\n" +
        "PDF TEXT:\n" + pdf_text.strip() + "\n"
    )

    # Choose the appropriate system prompt based on batch mode
    system_prompt = _BATCH_SYSTEM_PROMPT if is_batch else _SYSTEM_PROMPT

    # Add language instruction to system prompt if not English
    if language.lower() != "english":
        system_prompt += f"\n\nIMPORTANT: Generate the summary in {language} language, but keep field names in English for system compatibility."

    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]


def _post_chat(messages: List[Dict[str, str]], is_batch: bool = False, model: str = None) -> Dict[str, Any]:
    """Low-level HTTP POST helper with enhanced rate limiting and retry logic."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("Environment variable OPENAI_API_KEY is not set.")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    # Use provided model or default
    selected_model = model if model else MODEL

    payload = {
        "model": selected_model,
        "messages": messages,
        "temperature": 0.0,
        "response_format": {"type": "json_object"},
    }

    # Adjust timeout for batch requests
    timeout = TIMEOUT * 2 if is_batch else TIMEOUT

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            resp = requests.post(
                OPENAI_API_URL,
                headers=headers,
                json=payload,
                timeout=timeout,
            )

            # Handle rate limiting specifically
            if resp.status_code == 429:
                retry_after = int(resp.headers.get('retry-after', RATE_LIMIT_BACKOFF))
                log.warning(f"Rate limited (429). Waiting {retry_after}s before retry (attempt {attempt}/{MAX_RETRIES})")
                time.sleep(retry_after)
                continue

            resp.raise_for_status()
            data = resp.json()
            return data  # type: ignore[return-value]

        except requests.exceptions.Timeout:
            wait_time = BACKOFF_SECS ** attempt
            log.warning(f"Request timeout (attempt {attempt}/{MAX_RETRIES}). Waiting {wait_time}s")
            if attempt == MAX_RETRIES:
                raise
            time.sleep(wait_time)

        except (requests.RequestException, ValueError) as exc:
            wait_time = BACKOFF_SECS ** attempt
            log.warning(f"OpenAI request failed (attempt {attempt}/{MAX_RETRIES}): {exc}. Waiting {wait_time}s")
            if attempt == MAX_RETRIES:
                raise
            time.sleep(wait_time)
    # Unreachable – loop either returns or raises.


def _post_chat_with_fallback(messages: List[Dict[str, str]], is_batch: bool = False) -> Dict[str, Any]:
    """
    Enhanced chat function with intelligent model fallback for critical field extraction.

    Tries models in order of cost-effectiveness:
    1. GPT-4o-mini (cheapest)
    2. GPT-4.1-mini (mid-tier)
    3. GPT-4o (most expensive, most capable)

    If critical fields (company_name, document_year) are missing, retries with next model.
    """
    last_exception = None
    last_result = None

    for attempt, model in enumerate(FALLBACK_MODELS):
        try:
            log.info(f"Attempting analysis with {model} (attempt {attempt + 1}/{len(FALLBACK_MODELS)})")

            # Use enhanced prompt for retry attempts
            current_messages = messages
            if attempt > 0 and last_result:
                # Check what fields were missing in the previous attempt
                is_valid, missing_fields = _validate_critical_fields(last_result)
                if not is_valid:
                    current_messages = _enhance_prompt_for_retry(messages, missing_fields, attempt)
                    log.info(f"Enhanced prompt for retry - focusing on missing fields: {missing_fields}")

            # Make the API call with the current model
            raw_response = _post_chat(current_messages, is_batch, model)
            content = raw_response["choices"][0]["message"]["content"].strip()

            # Parse the JSON response
            result = json.loads(content)

            # Validate critical fields
            is_valid, missing_fields = _validate_critical_fields(result)

            if is_valid:
                log.info(f"✅ Successful extraction with {model} - all critical fields present")
                return result
            else:
                log.warning(f"⚠️ {model} missing critical fields: {missing_fields}")
                last_result = result

                # If this is the last model, return the best result we have
                if attempt == len(FALLBACK_MODELS) - 1:
                    log.error(f"❌ All models failed to extract critical fields. Returning best attempt.")
                    return result

                # Continue to next model
                continue

        except json.JSONDecodeError as e:
            log.error(f"JSON parsing failed with {model}: {e}")
            last_exception = e
            continue

        except Exception as e:
            log.error(f"Request failed with {model}: {e}")
            last_exception = e
            continue

    # If all models failed, raise the last exception or return fallback
    if last_exception:
        raise last_exception

    # Fallback result if everything fails
    return {
        "doc_type": "Unknown",
        "summary": "All model attempts failed to extract critical information.",
        "extracted_fields": {
            "company_name": "Unknown",
            "document_year": 2024,
            "_fallback_used": True
        },
    }


def analyze_mail_and_pdf(mail_body: str, pdf_text: str, language: str = "English", is_batch: bool = False, use_batch_manager: bool = True) -> Dict[str, Any]:
    """Send *mail_body* and *pdf_text* to ChatGPT-4o and return its JSON dict.

    If the request fails the function logs the error and returns a fallback
    result so that the caller can keep running without the ML component.

    Args:
        mail_body: The email body text
        pdf_text: The extracted PDF text
        language: The preferred language for the summary (default: English)
        is_batch: Whether this is batch processing of multiple documents (default: False)
        use_batch_manager: Whether to use the batch manager for rate limiting (default: True)
    """
    if use_batch_manager:
        # Use batch manager for rate limiting
        request_id = f"single_{int(time.time() * 1000)}"
        document_texts = [{"filename": "document.pdf", "text": pdf_text}]

        _batch_manager.add_request(request_id, mail_body, document_texts, language, is_batch)
        _batch_manager.process_queue()

        result = _batch_manager.get_result(request_id, timeout=120)
        if result:
            return result
        else:
            log.error("Batch manager timeout - falling back to direct processing")

    # Fallback to direct processing with intelligent model fallback
    messages = _build_messages(mail_body, pdf_text, language, is_batch)

    try:
        # Use the new fallback mechanism for better extraction
        result = _post_chat_with_fallback(messages, is_batch)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT analysis failed with all models – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT analysis failed with all models.",
            "extracted_fields": {
                "company_name": "Unknown",
                "document_year": 2024,
                "_fallback_used": True
            },
        }


def analyze_mail_and_batch_pdfs(mail_body: str, document_texts: List[Dict[str, str]], language: str = "English", use_batch_manager: bool = True) -> Dict[str, Any]:
    """Send *mail_body* and multiple *document_texts* to ChatGPT-4o for batch analysis.

    Args:
        mail_body: The email body text
        document_texts: List of dicts with 'filename' and 'text' keys
        language: The preferred language for the summary (default: English)
        use_batch_manager: Whether to use the batch manager for rate limiting (default: True)

    Returns:
        Dict with doc_type, summary, and extracted_fields
    """
    if use_batch_manager:
        # Use batch manager for rate limiting
        request_id = f"batch_{int(time.time() * 1000)}"

        _batch_manager.add_request(request_id, mail_body, document_texts, language, is_batch=True)
        _batch_manager.process_queue()

        result = _batch_manager.get_result(request_id, timeout=180)  # Longer timeout for batch
        if result:
            return result
        else:
            log.error("Batch manager timeout - falling back to direct processing")

    # Fallback to direct processing
    # Combine all document texts into a single prompt
    combined_text = ""
    for i, doc in enumerate(document_texts, 1):
        filename = doc.get('filename', f'Document {i}')
        text = doc.get('text', '')
        combined_text += f"\n\n=== DOCUMENT {i}: {filename} ===\n{text}"

    # Use batch processing mode with intelligent model fallback
    messages = _build_messages(mail_body, combined_text, language, is_batch=True)

    try:
        # Use the new fallback mechanism for better extraction
        result = _post_chat_with_fallback(messages, is_batch=True)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT batch analysis failed with all models – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT batch analysis failed with all models.",
            "extracted_fields": {
                "company_name": "Unknown",
                "document_year": 2024,
                "_fallback_used": True
            },
        }