"""OpenAI ChatGPT-4o integration helpers with advanced rate limiting and batching.

This module provides intelligent document processing with OpenAI's API, featuring:
- Request queue system with rate limiting
- Intelligent batching for multiple documents
- Enhanced retry logic with exponential backoff
- Proper handling of 429 (Too Many Requests) errors

Environment variables
---------------------
OPENAI_API_KEY
    The secret API key used for authenticating with the OpenAI service.

Any networking or JSON related error is retried with exponential back-off up to
``MAX_RETRIES`` times. If all attempts fail the function returns a minimal
fallback dict so that the caller can continue processing without crashing.
"""

from __future__ import annotations

from dotenv import load_dotenv
load_dotenv()

from typing import Dict, Any, List, Optional, Tuple
import os
import json
import time
import logging
import requests
import threading
from queue import Queue, Empty
from dataclasses import dataclass
from datetime import datetime, timedelta

__all__ = ["analyze_mail_and_pdf", "analyze_mail_and_batch_pdfs", "BatchRequestManager"]

log = logging.getLogger(__name__)

OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"
MODEL = "gpt-4.1-mini"  # GPT-4o, GPT-4o-mini or GPT-4.1-mini
TIMEOUT = 60  # Increased timeout for batch requests
MAX_RETRIES = 5  # Increased retries for rate limiting
BACKOFF_SECS = 2

# Rate limiting configuration
REQUESTS_PER_MINUTE = 50  # Conservative limit for GPT-4o
MIN_REQUEST_INTERVAL = 60.0 / REQUESTS_PER_MINUTE  # Minimum seconds between requests
RATE_LIMIT_BACKOFF = 60  # Seconds to wait on 429 error


@dataclass
class BatchRequest:
    """Represents a single request in the batch processing queue."""
    request_id: str
    mail_body: str
    document_texts: List[Dict[str, str]]  # List of {filename, text} dicts
    language: str
    is_batch: bool
    timestamp: datetime
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    completed: bool = False


class BatchRequestManager:
    """Manages OpenAI API requests with intelligent batching and rate limiting."""

    def __init__(self):
        self._request_queue = Queue()
        self._results = {}
        self._last_request_time = 0
        self._lock = threading.Lock()
        self._processing = False

    def add_request(self, request_id: str, mail_body: str, document_texts: List[Dict[str, str]],
                   language: str = "English", is_batch: bool = False) -> str:
        """Add a request to the processing queue."""
        request = BatchRequest(
            request_id=request_id,
            mail_body=mail_body,
            document_texts=document_texts,
            language=language,
            is_batch=is_batch,
            timestamp=datetime.now()
        )
        self._request_queue.put(request)
        return request_id

    def get_result(self, request_id: str, timeout: float = 300) -> Optional[Dict[str, Any]]:
        """Get the result for a specific request, waiting up to timeout seconds."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            with self._lock:
                if request_id in self._results:
                    result = self._results.pop(request_id)
                    return result
            time.sleep(0.1)
        return None

    def process_queue(self) -> None:
        """Process all requests in the queue with proper rate limiting."""
        if self._processing:
            return

        self._processing = True
        try:
            while not self._request_queue.empty():
                try:
                    request = self._request_queue.get_nowait()
                    self._process_single_request(request)
                except Empty:
                    break
        finally:
            self._processing = False

    def _process_single_request(self, request: BatchRequest) -> None:
        """Process a single request with rate limiting."""
        # Ensure minimum interval between requests
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        if time_since_last < MIN_REQUEST_INTERVAL:
            sleep_time = MIN_REQUEST_INTERVAL - time_since_last
            log.info(f"Rate limiting: sleeping {sleep_time:.2f}s before next request")
            time.sleep(sleep_time)

        try:
            if request.is_batch and len(request.document_texts) > 1:
                result = self._process_batch_request(request)
            else:
                # Single document processing
                doc_text = request.document_texts[0]['text'] if request.document_texts else ""
                result = self._process_single_document(request.mail_body, doc_text, request.language)

            with self._lock:
                self._results[request.request_id] = result

        except Exception as e:
            log.error(f"Failed to process request {request.request_id}: {e}")
            with self._lock:
                self._results[request.request_id] = {
                    "doc_type": "Unknown",
                    "summary": f"Processing failed: {str(e)}",
                    "extracted_fields": {},
                }
        finally:
            self._last_request_time = time.time()

    def _process_batch_request(self, request: BatchRequest) -> Dict[str, Any]:
        """Process a batch request with multiple documents."""
        # Combine all document texts into a single prompt
        combined_text = ""
        for i, doc in enumerate(request.document_texts, 1):
            filename = doc.get('filename', f'Document {i}')
            text = doc.get('text', '')
            combined_text += f"\n\n=== DOCUMENT {i}: {filename} ===\n{text}"

        messages = _build_messages(request.mail_body, combined_text, request.language, is_batch=True)

        try:
            raw = _post_chat(messages, is_batch=True)
            content = raw["choices"][0]["message"]["content"].strip()
            log.debug("RAW ChatGPT batch reply: %s", content[:500])
            result: Dict[str, Any] = json.loads(content)
            return result
        except Exception as exc:
            log.error("ChatGPT batch analysis failed – falling back to defaults: %s", exc)
            return {
                "doc_type": "Unknown",
                "summary": "ChatGPT batch analysis failed.",
                "extracted_fields": {},
            }

    def _process_single_document(self, mail_body: str, pdf_text: str, language: str) -> Dict[str, Any]:
        """Process a single document."""
        messages = _build_messages(mail_body, pdf_text, language, is_batch=False)

        try:
            raw = _post_chat(messages, is_batch=False)
            content = raw["choices"][0]["message"]["content"].strip()
            log.debug("RAW ChatGPT reply: %s", content[:500])
            result: Dict[str, Any] = json.loads(content)
            return result
        except Exception as exc:
            log.error("ChatGPT analysis failed – falling back to defaults: %s", exc)
            return {
                "doc_type": "Unknown",
                "summary": "ChatGPT analysis failed.",
                "extracted_fields": {},
            }


# Global batch manager instance
_batch_manager = BatchRequestManager()


_SYSTEM_PROMPT = (
    "You are a document processing assistant for email automation. Analyze email + PDF to extract: "
    "(1) document type (invoice, certificate of analysis, safety data sheet, etc.), "
    "(2) key data for downstream systems, (3) structured summary with actionable insights.\n\n"
    "EMAIL ANALYSIS:\n"
    "• Extract instructions, requests, specific guidance from email body\n"
    "• Capture ALL contact info: phone numbers, extensions, emails, emergency contacts\n"
    "• Note deadlines, urgency indicators, special handling instructions\n"
    "• Include actual URLs/links mentioned (not generic 'provided link' text)\n\n"
    "EXTRACTION RULES:\n"
    "• Batch/lot/serial numbers: Extract ONLY alphanumeric IDs (e.g. '6098403', 'ABC123'), NOT dates\n"
    "• Dates: Extract separately as specific fields (manufacturing_date, expiry_date, test_date, etc.)\n"
    "• CRITICAL: 'Batch: 2018-11-08' → batch_number: '6098403' (find actual ID), manufacturing_date: '2018-11-08'\n"
    "• MANDATORY company_name extraction (priority: headers→email domain→logos→contact info):\n"
    "  - Certificates/CoA: manufacturer/supplier name\n"
    "  - Invoices/Orders: vendor/seller name\n"
    "  - Format: Remove legal suffixes (AB, Inc, Ltd, etc.) → 'TechShop AB' → 'TechShop'\n"
    "  - NEVER empty/null/Unknown - always extract from available sources\n"
    "• Use descriptive field names: batch_number, invoice_number, po_number, etc.\n"
    "• MANDATORY document_year: 4-digit year most relevant to document (manufacturing/test/creation year)\n\n"
    "SMART ANALYSIS:\n"
    "• For technical docs (CoA, test reports, safety sheets): Compare results vs specifications\n"
    "• ONLY report concerning parameters (out-of-spec, close to limits, safety-critical)\n"
    "• Summarize compliant parameters: 'All other parameters within specification'\n"
    "• Provide actionable guidance:\n"
    "  - All compliant: 'Optional review'\n"
    "  - Minor issues: 'Attention recommended for [areas]'\n"
    "  - Major issues: 'Attention required for [parameters]'\n"
    "  - Critical: 'URGENT review - critical issues identified'\n\n"
    "SUMMARY FORMAT (use exact structure):\n"
    "--- DOCUMENT INFO ---\n"
    "[Type, identifiers, dates]\n"
    "--- KEY FINDINGS ---\n"
    "[Critical results, compliance status - concerning parameters only]\n"
    "--- EMAIL INSTRUCTIONS ---\n"
    "[Contact info, deadlines, actual URLs, specific requests]\n"
    "--- ACTION REQUIRED ---\n"
    "[Clear recommendations]\n\n"
    "Use bullet points (•), include all contact details, avoid redundant phrases.\n\n"
    "Return ONLY valid JSON: {\"doc_type\": \"\", \"summary\": \"\", \"extracted_fields\": {}}"
)

_BATCH_SYSTEM_PROMPT = (
    "You are a document processing assistant for email automation. Analyze email + MULTIPLE PDFs of same type to extract: "
    "(1) document type, (2) key data, (3) COMPACT summary highlighting ONLY concerns/issues.\n\n"
    "BATCH RULES:\n"
    "• Process multiple documents from ONE email\n"
    "• List all documents, highlight ONLY concerns/issues\n"
    "• Mention compliant documents briefly in one line\n"
    "• STOP after EMAIL INSTRUCTIONS - no document repetition\n\n"
    "EMAIL ANALYSIS:\n"
    "• Extract instructions, requests, specific guidance from email body\n"
    "• Capture ALL contact info: phone numbers, extensions, emails, emergency contacts\n"
    "• Note deadlines, urgency indicators, special handling instructions\n"
    "• Include actual URLs/links mentioned (not generic 'provided link' text)\n\n"
    "EXTRACTION RULES:\n"
    "• Batch/lot/serial: Extract ONLY alphanumeric IDs, NOT dates\n"
    "• MANDATORY company_name: Remove legal suffixes, never empty/Unknown\n"
    "• MANDATORY document_year: 4-digit year most relevant to document\n"
    "• Extract: identifiers, amounts, dates, contact info, compliance data\n\n"
    "BATCH ANALYSIS:\n"
    "• For technical docs: Compare results vs specs, report ONLY concerning parameters\n"
    "• Summarize compliant: 'All other parameters within specification'\n"
    "• Assess overall compliance across all documents\n\n"
    "BATCH SUMMARY FORMAT (STOP after EMAIL INSTRUCTIONS):\n"
    "--- DOCUMENT INFO ---\n"
    "1. [filename]: [brief description]\n"
    "--- KEY FINDINGS ---\n"
    "• [document]: [only concerning findings]\n"
    "• [compliant docs]: All other documents within specification\n"
    "--- EMAIL INSTRUCTIONS ---\n"
    "[contact info, deadlines, actual URLs]\n"
    "STOP HERE - no additional sections.\n\n"
    "Return ONLY valid JSON: {\"doc_type\": \"\", \"summary\": \"\", \"extracted_fields\": {}}"
)


def _build_messages(mail_body: str, pdf_text: str, language: str = "English", is_batch: bool = False) -> List[Dict[str, str]]:
    """Compose the messages list for the Chat Completions endpoint."""

    user_prompt = (
        "EMAIL BODY:\n" + mail_body.strip() + "\n\n" +
        "PDF TEXT:\n" + pdf_text.strip() + "\n"
    )

    # Choose the appropriate system prompt based on batch mode
    system_prompt = _BATCH_SYSTEM_PROMPT if is_batch else _SYSTEM_PROMPT

    # Add language instruction to system prompt if not English
    if language.lower() != "english":
        system_prompt += f"\n\nIMPORTANT: Generate the summary in {language} language, but keep field names in English for system compatibility."

    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]


def _post_chat(messages: List[Dict[str, str]], is_batch: bool = False) -> Dict[str, Any]:
    """Low-level HTTP POST helper with enhanced rate limiting and retry logic."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("Environment variable OPENAI_API_KEY is not set.")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    payload = {
        "model": MODEL,
        "messages": messages,
        "temperature": 0.0,
        "response_format": {"type": "json_object"},
    }

    # Adjust timeout for batch requests
    timeout = TIMEOUT * 2 if is_batch else TIMEOUT

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            resp = requests.post(
                OPENAI_API_URL,
                headers=headers,
                json=payload,
                timeout=timeout,
            )

            # Handle rate limiting specifically
            if resp.status_code == 429:
                retry_after = int(resp.headers.get('retry-after', RATE_LIMIT_BACKOFF))
                log.warning(f"Rate limited (429). Waiting {retry_after}s before retry (attempt {attempt}/{MAX_RETRIES})")
                time.sleep(retry_after)
                continue

            resp.raise_for_status()
            data = resp.json()
            return data  # type: ignore[return-value]

        except requests.exceptions.Timeout:
            wait_time = BACKOFF_SECS ** attempt
            log.warning(f"Request timeout (attempt {attempt}/{MAX_RETRIES}). Waiting {wait_time}s")
            if attempt == MAX_RETRIES:
                raise
            time.sleep(wait_time)

        except (requests.RequestException, ValueError) as exc:
            wait_time = BACKOFF_SECS ** attempt
            log.warning(f"OpenAI request failed (attempt {attempt}/{MAX_RETRIES}): {exc}. Waiting {wait_time}s")
            if attempt == MAX_RETRIES:
                raise
            time.sleep(wait_time)
    # Unreachable – loop either returns or raises.


def analyze_mail_and_pdf(mail_body: str, pdf_text: str, language: str = "English", is_batch: bool = False, use_batch_manager: bool = True) -> Dict[str, Any]:
    """Send *mail_body* and *pdf_text* to ChatGPT-4o and return its JSON dict.

    If the request fails the function logs the error and returns a fallback
    result so that the caller can keep running without the ML component.

    Args:
        mail_body: The email body text
        pdf_text: The extracted PDF text
        language: The preferred language for the summary (default: English)
        is_batch: Whether this is batch processing of multiple documents (default: False)
        use_batch_manager: Whether to use the batch manager for rate limiting (default: True)
    """
    if use_batch_manager:
        # Use batch manager for rate limiting
        request_id = f"single_{int(time.time() * 1000)}"
        document_texts = [{"filename": "document.pdf", "text": pdf_text}]

        _batch_manager.add_request(request_id, mail_body, document_texts, language, is_batch)
        _batch_manager.process_queue()

        result = _batch_manager.get_result(request_id, timeout=120)
        if result:
            return result
        else:
            log.error("Batch manager timeout - falling back to direct processing")

    # Fallback to direct processing
    messages = _build_messages(mail_body, pdf_text, language, is_batch)

    try:
        raw = _post_chat(messages, is_batch)
        content = raw["choices"][0]["message"]["content"].strip()
        # Debug: log first 500 chars of the model's reply to diagnose JSON issues
        log.debug("RAW ChatGPT reply: %s", content[:500])
        # The model has been instructed to output JSON only.  Parse it.
        result: Dict[str, Any] = json.loads(content)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT analysis failed – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT analysis failed.",
            "extracted_fields": {},
        }


def analyze_mail_and_batch_pdfs(mail_body: str, document_texts: List[Dict[str, str]], language: str = "English", use_batch_manager: bool = True) -> Dict[str, Any]:
    """Send *mail_body* and multiple *document_texts* to ChatGPT-4o for batch analysis.

    Args:
        mail_body: The email body text
        document_texts: List of dicts with 'filename' and 'text' keys
        language: The preferred language for the summary (default: English)
        use_batch_manager: Whether to use the batch manager for rate limiting (default: True)

    Returns:
        Dict with doc_type, summary, and extracted_fields
    """
    if use_batch_manager:
        # Use batch manager for rate limiting
        request_id = f"batch_{int(time.time() * 1000)}"

        _batch_manager.add_request(request_id, mail_body, document_texts, language, is_batch=True)
        _batch_manager.process_queue()

        result = _batch_manager.get_result(request_id, timeout=180)  # Longer timeout for batch
        if result:
            return result
        else:
            log.error("Batch manager timeout - falling back to direct processing")

    # Fallback to direct processing
    # Combine all document texts into a single prompt
    combined_text = ""
    for i, doc in enumerate(document_texts, 1):
        filename = doc.get('filename', f'Document {i}')
        text = doc.get('text', '')
        combined_text += f"\n\n=== DOCUMENT {i}: {filename} ===\n{text}"

    # Use batch processing mode
    messages = _build_messages(mail_body, combined_text, language, is_batch=True)

    try:
        raw = _post_chat(messages, is_batch=True)
        content = raw["choices"][0]["message"]["content"].strip()
        # Debug: log first 500 chars of the model's reply to diagnose JSON issues
        log.debug("RAW ChatGPT batch reply: %s", content[:500])
        # The model has been instructed to output JSON only.  Parse it.
        result: Dict[str, Any] = json.loads(content)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT batch analysis failed – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT batch analysis failed.",
            "extracted_fields": {},
        }